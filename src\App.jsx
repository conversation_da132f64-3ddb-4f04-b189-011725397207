import { useEffect, useState } from "react";
import { FiSearch } from "react-icons/fi";
import "./App.css";

export default function App() {
  const [tricks, setTricks] = useState([]);
  const [search, setSearch] = useState("");
  const [filters, setFilters] = useState([]);
  const [showFilters, setShowFilters] = useState(false);
  const [isAlphabetized, setIsAlphabetized] = useState(false);
  const [expandedTrick, setExpandedTrick] = useState(null); // NEW: track expanded trick

  useEffect(() => {
    fetch("/tricks.json")
      .then((res) => res.json())
      .then((data) => setTricks(data));
  }, []);

  const toggleFilter = (f) => {
    setFilters((prev) =>
      prev.includes(f) ? prev.filter((x) => x !== f) : [...prev, f]
    );
  };

  const toggleAlphabetize = () => {
    setIsAlphabetized(!isAlphabetized);
  };

  const toggleExpand = (id) => {
    setExpandedTrick(expandedTrick === id ? null : id);
  };

  // convert "4:21" → seconds (261)
  const parseTimestamp = (ts) => {
    if (!ts) return 0;
    const parts = ts.split(":").map(Number);
    if (parts.length === 2) {
      return parts[0] * 60 + parts[1];
    }
    return 0;
  };

  const filteredTricks = tricks.filter((t) => {
    const matchesSearch = t.name.toLowerCase().includes(search.toLowerCase());

    const stanceFilters = filters.filter((f) =>
      ["regular", "fakie", "switch", "nollie"].includes(f)
    );
    const typeFilters = filters.filter(
      (f) => !["regular", "fakie", "switch", "nollie"].includes(f)
    );

    let matchesStance =
      stanceFilters.length === 0 ||
      stanceFilters.includes(t.stance.toLowerCase());
    let matchesType =
      typeFilters.length === 0 ||
      t.type.some((ty) => typeFilters.includes(ty.toLowerCase()));

    if (stanceFilters.length > 0) {
      matchesType =
        typeFilters.length === 0 ||
        (matchesStance &&
          t.type.some((ty) => typeFilters.includes(ty.toLowerCase())));
    }

    return matchesSearch && matchesStance && matchesType;
  });

  const displayTricks = isAlphabetized
    ? [...filteredTricks].sort((a, b) => a.name.localeCompare(b.name))
    : filteredTricks;

  return (
    <div className="app">
      <h1>Flatground Trick Index</h1>

      {/* Search */}
      <div className="search-bar">
        <FiSearch className="icon" />
        <input
          type="text"
          placeholder="Search tricks..."
          value={search}
          onChange={(e) => setSearch(e.target.value)}
        />
      </div>

      {/* Filter Header with toggle & clear */}
      <div className="filter-header">
        <button onClick={() => setShowFilters(!showFilters)}>
          {showFilters ? "Hide Filters" : "Show Filters"}
        </button>
        <div className="filter-header-right">
          <button onClick={toggleAlphabetize}>
            {isAlphabetized ? "Original Order" : "Alphabetize"}
          </button>
          <button onClick={() => setFilters([])}>Clear Filters</button>
        </div>
      </div>

      {/* Filters */}
      {showFilters && (
        <div className="filters">
          {/* stance filters */}
          <div className="filter-section">
            <h4>Stances</h4>
            <div className="filter-buttons">
              {["regular", "fakie", "switch", "nollie"].map((f) => (
                <button
                  key={f}
                  onClick={() => toggleFilter(f)}
                  className={filters.includes(f) ? "active" : ""}
                >
                  {f}
                </button>
              ))}
            </div>
          </div>

          {/* rotation filters */}
          <div className="filter-section">
            <h4>Rotations</h4>
            <div className="filter-buttons">
              {["180", "360", "540"].map((f) => (
                <button
                  key={f}
                  onClick={() => toggleFilter(f)}
                  className={filters.includes(f) ? "active" : ""}
                >
                  {f}°
                </button>
              ))}
            </div>
          </div>

          {/* flip type filters */}
          <div className="filter-section">
            <h4>Flip Types</h4>
            <div className="filter-buttons">
              {[
                "flip",
                "kickflip",
                "heelflip",
                "varial",
                "pressure",
                "hardflip",
                "inward",
                "lazer",
                "feather flip",
                "forward flip",
                "impossible",
                "double",
                "triple",
                "anti casper",
              ].map((f) => (
                <button
                  key={f}
                  onClick={() => toggleFilter(f)}
                  className={filters.includes(f) ? "active" : ""}
                >
                  {f}
                </button>
              ))}
            </div>
          </div>

          {/* shuv / spins */}
          <div className="filter-section">
            <h4>Shuv / Spins</h4>
            <div className="filter-buttons">
              {[
                "shuv",
                "pop shove-it",
                "big spin",
                "big flip",
                "big heelflip",
                "biggerspin",
                "gazelle spin",
                "body varial",
                "antibigspin",
              ].map((f) => (
                <button
                  key={f}
                  onClick={() => toggleFilter(f)}
                  className={filters.includes(f) ? "active" : ""}
                >
                  {f}
                </button>
              ))}
            </div>
          </div>

          {/* other */}
          <div className="filter-section">
            <h4>Other / Modifiers</h4>
            <div className="filter-buttons">
              {[
                "late",
                "front foot",
                "underflip",
                "cancel",
                "plasma",
                "half",
                "big",
                "bigger",
              ].map((f) => (
                <button
                  key={f}
                  onClick={() => toggleFilter(f)}
                  className={filters.includes(f) ? "active" : ""}
                >
                  {f}
                </button>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Trick List */}
      {displayTricks.length === 0 ? (
        <p className="empty">No tricks found 😔</p>
      ) : (
        <ul className="trick-list">
          {displayTricks.map((t) => {
            const startTime = parseTimestamp(t.timestamp);

            let src = "";
            switch (t.video) {
              case 1:
                src = "8bxg4YCo2RE";
                break;
              case 2:
                src = "mN6_vgbRD7Y";
                break;
              case 3:
                src = "N4sgk0PLhQ0";
                break;
              case 4:
                src = "uqV5JQU4rG8";
                break;
              default:
                src = "N4sgk0PLhQ0"; // fallback if missing
                break;
            }

            const videoSrc = `https://www.youtube.com/embed/${src}?start=${startTime}`;

            return (
              <li key={t.id} className="trick-item">
                <div
                  className="trick-header"
                  onClick={() => toggleExpand(t.id)}
                >
                  <div className="trick-name">{t.name}</div>
                  <div className="trick-meta">
                    {t.stance} • {t.type.join(", ")}
                  </div>
                </div>

                {expandedTrick === t.id && (
                  <div className="trick-video">
                    <iframe
                      width="100%"
                      height="360"
                      src={videoSrc}
                      title={t.name}
                      frameBorder="0"
                      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                      allowFullScreen
                    ></iframe>
                  </div>
                )}
              </li>
            );
          })}
        </ul>
      )}
    </div>
  );
}
